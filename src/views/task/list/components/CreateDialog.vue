<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑任务' : '创建任务'"
    width="500px"
    :before-close="handleClose"
    append-to-body
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      label-position="left"
    >
      <el-form-item label="任务名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入任务名称" />
      </el-form-item>
      <el-form-item label="选择数据类型" prop="taskType">
        <el-select
          v-model="formData.taskType"
          placeholder="请选择数据类型"
          clearable
          style="width: 100%"
        >
          <el-option
            v-for="(label, value) in TASK_TYPE_LABELS"
            :key="value"
            :label="label"
            :value="Number(value)"
          />
        </el-select>
      </el-form-item>

      <!-- 根据任务类型动态显示数据源选择 -->
      <el-form-item
        v-if="formData.taskType === TASK_TYPES.DATASET"
        label="选择数据集"
        prop="taskDataId"
      >
        <el-select
          v-model="formData.taskDataId"
          placeholder="请选择数据集"
          clearable
          filterable
          remote
          :remote-method="searchDataset"
          :loading="datasetLoading"
          style="width: 100%"
        >
          <el-option
            v-for="item in datasetList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item
        v-if="formData.taskType === TASK_TYPES.VIDEO_STREAM"
        label="选择视频流"
        prop="taskDataId"
      >
        <el-select
          v-model="formData.taskDataId"
          placeholder="请选择视频流"
          clearable
          filterable
          remote
          :remote-method="searchVideoStream"
          :loading="videoStreamLoading"
          style="width: 100%"
        >
          <el-option
            v-for="item in videoStreamList"
            :key="item.id"
            :label="item.deviceName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="选择模型" prop="aiModelId">
        <el-select
          v-model="formData.aiModelId"
          placeholder="请选择模型"
          clearable
          filterable
          remote
          :remote-method="searchModel"
          :loading="modelLoading"
          style="width: 100%"
        >
          <el-option
            v-for="item in modelList"
            :key="item.id"
            :label="item.modelName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="预警通知人员" prop="alarmNoticeUserId">
        <el-select
          v-model="formData.alarmNoticeUserId"
          placeholder="请选择预警通知人员"
          multiple
          clearable
          filterable
          remote
          :remote-method="searchUsers"
          :loading="userLoading"
          style="width: 100%"
        >
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.nickname"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleFastFill">快速填充</el-button>
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { createTask, updateTask } from '@/api/alg/task'
import { TASK_TYPE_LABELS, TASK_TYPES } from '../constants'
import { getDatasetPage } from '@/api/alg/dataset'
import { getDevicePage } from '@/api/alg/device'
import { getModelPage } from '@/api/alg/model'
import { getUserSimplePage } from '@/api/system/user'
import { useRemoteSelect } from './useRemoteSelect'

defineOptions({ name: 'TaskFormDialog' })

const emit = defineEmits<{
  success: []
}>()

interface FormData {
  id?: number
  name?: string
  taskType?: number
  taskDataId?: number
  aiModelId?: number
  alarmNoticeUserId: number[]
}

const getInitialData = (): FormData => ({
  id: undefined,
  name: undefined,
  taskType: undefined,
  taskDataId: undefined,
  aiModelId: undefined,
  alarmNoticeUserId: []
})

const dialogVisible = ref(false)
const loading = ref(false)
const formRef = ref<FormInstance>()
const formData = reactive<FormData>(getInitialData())

const isEdit = ref(false)

const {
  list: datasetList,
  loading: datasetLoading,
  search: searchDataset
} = useRemoteSelect<{ id: number; name: string }>(getDatasetPage, 'name', {}, '获取数据集列表失败')

const {
  list: videoStreamList,
  loading: videoStreamLoading,
  search: searchVideoStream
} = useRemoteSelect<{ id: number; deviceName: string }>(
  getDevicePage,
  'deviceName',
  {},
  '获取视频流列表失败'
)

const {
  list: modelList,
  loading: modelLoading,
  search: searchModel
} = useRemoteSelect<{ id: number; modelName: string }>(
  getModelPage,
  'modelName',
  {},
  '获取模型列表失败'
)

const {
  list: userList,
  loading: userLoading,
  search: searchUsers
} = useRemoteSelect<{ id: number; nickname: string }>(
  getUserSimplePage,
  'username',
  {},
  '获取用户列表失败'
)

const formRules: FormRules = {
  name: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
  taskType: [{ required: true, message: '请选择数据类型', trigger: 'change' }],
  taskDataId: [{ required: true, message: '请选择数据源', trigger: 'change' }],
  aiModelId: [{ required: true, message: '请选择模型', trigger: 'change' }],
  alarmNoticeUserId: [{ required: true, message: '请选择预警通知人员', trigger: 'change' }]
}

/** 加载任务数据并预加载选项 */
const loadTaskData = async (task: any) => {
  // 设置基本表单数据
  Object.assign(formData, {
    id: task.id,
    name: task.name,
    taskType: Number(task.taskType),
    taskDataId: task.taskDataId ? Number(task.taskDataId) : undefined,
    aiModelId: task.aiModelId ? Number(task.aiModelId) : undefined,
    alarmNoticeUserId: task.alarmNoticeUserId
      ? typeof task.alarmNoticeUserId === 'string'
        ? task.alarmNoticeUserId.split(',').map((id) => Number(id))
        : task.alarmNoticeUserId
      : []
  })

  // 预加载选项数据
  try {
    // 根据任务类型预加载数据源选项
    if (task.taskType === TASK_TYPES.DATASET && task.taskDataId) {
      await searchDataset('')
      // 确保当前选中的数据集在列表中，注意类型转换
      const taskDataId = Number(task.taskDataId)
      if (!datasetList.value.find((item) => item.id === taskDataId)) {
        datasetList.value.unshift({
          id: taskDataId,
          name: task.taskDataName || `数据集${taskDataId}`
        })
      }
    } else if (task.taskType === TASK_TYPES.VIDEO_STREAM && task.taskDataId) {
      await searchVideoStream('')
      // 确保当前选中的视频流在列表中，注意类型转换
      const taskDataId = Number(task.taskDataId)
      if (!videoStreamList.value.find((item) => item.id === taskDataId)) {
        videoStreamList.value.unshift({
          id: taskDataId,
          deviceName: task.taskDataName || `设备${taskDataId}`
        })
      }
    }

    // 预加载模型选项
    if (task.aiModelId) {
      await searchModel('')
      // 确保当前选中的模型在列表中，注意类型转换
      const aiModelId = Number(task.aiModelId)
      if (!modelList.value.find((item) => item.id === aiModelId)) {
        modelList.value.unshift({
          id: aiModelId,
          modelName: task.aiModelName || `模型${aiModelId}`
        })
      }
    }

    // 预加载用户选项
    if (formData.alarmNoticeUserId.length > 0) {
      await searchUsers('')
      // 确保当前选中的用户在列表中
      formData.alarmNoticeUserId.forEach((userId) => {
        if (!userList.value.find((item) => item.id === userId)) {
          userList.value.push({ id: userId, nickname: `用户${userId}` })
        }
      })
    }
  } catch (error) {
    console.error('预加载选项数据失败:', error)
  }
}

/** 打开弹窗 */
const open = async (task?: any) => {
  dialogVisible.value = true
  resetForm()
  if (task) {
    isEdit.value = true
    await loadTaskData(task)
  } else {
    isEdit.value = false
  }
}

/** 关闭弹窗 */
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

/** 重置表单 */
const resetForm = () => {
  Object.assign(formData, getInitialData())
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

/** 提交表单 */
const handleSubmit = async () => {
  if (!formRef.value) return

  const isValid = await formRef.value.validate().catch(() => false)
  if (!isValid) return

  loading.value = true
  try {
    const payload = {
      name: formData.name,
      taskType: formData.taskType!,
      taskDataId: formData.taskDataId!,
      aiModelId: formData.aiModelId!,
      ...(formData.alarmNoticeUserId.length > 0 && {
        alarmNoticeUserId: formData.alarmNoticeUserId.join(',')
      })
    }
    if (isEdit.value) {
      await updateTask({ ...payload, id: formData.id })
      ElMessage.success('更新成功')
    } else {
      await createTask(payload)
      ElMessage.success('创建成功')
    }
    handleClose()
    emit('success')
  } catch (error) {
    ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
    console.error(isEdit.value ? '更新任务失败:' : '创建任务失败:', error)
  } finally {
    loading.value = false
  }
}

const handleFastFill = () => {
  formData.name = '测试任务'
  formData.taskType = 0
  formData.taskDataId = 1
  formData.aiModelId = 1
  formData.alarmNoticeUserId = [1]
}

watch(
  () => formData.taskType,
  () => {
    // 当任务类型切换时，重置数据源选择
    formData.taskDataId = undefined
  }
)

defineExpose({
  open
})
</script>

<style scoped>
.upload-tip {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.dialog-footer {
  text-align: right;
}
</style>
